from hummingbot.strategy.script_strategy_base import ScriptStrategyBase
from dataclasses import dataclass
from typing import Optional
import asyncio
from hummingbot.core.network_iterator import NetworkStatus
from decimal import Decimal
from hummingbot.core.data_type.common import OrderType
from hummingbot.core.data_type.in_flight_order import OrderState
from hummingbot.core.data_type.common import PositionAction
from hummingbot.client.config.config_data_types import BaseClientModel
from pydantic import Field

@dataclass
class TokenPair:
    spot_exchange: str
    perp_exchange: str
    spot_trading_pair: str
    perp_trading_pair: str
    quantity_ratio: float  # 现货/合约
    base_amount: float     # 以现货为基准
    leverage: int         # 合约杠杆
    max_round: int   # 最大开仓次数

class FundingOpenConfig(BaseClientModel):
    spot_exchange: str = Field(..., description="现货交易所名称")
    perp_exchange: str = Field(..., description="合约交易所名称")
    spot_trading_pair: str = Field(..., description="现货交易对")
    perp_trading_pair: str = Field(..., description="合约交易对")
    quantity_ratio: float = Field(..., description="现货/合约数量比")
    base_amount: float = Field(..., description="每次操作现货数量")
    leverage: int = Field(..., description="合约杠杆")
    max_round: int = Field(..., description="最大操作次数")
    spread_threshold: float = Field(..., description="操作时的价差条件")
    consecutive_threshold: int = Field(..., description="操作时的价差条件连续成立次数")
    avg_price_diff_threshold: float = Field(..., description="操作后实际成交价格的价差比阈值")
    debug_mode: bool = Field(..., description="调试模式")
    operation_type: str = Field(..., description="操作类型: open(开仓) 或 close(关仓)")

class FundingOpen(ScriptStrategyBase):
    @classmethod
    def init_markets(cls, config: FundingOpenConfig):
        cls.markets = {
            config.spot_exchange: {config.spot_trading_pair},
            config.perp_exchange: {config.perp_trading_pair}
        }

    def __init__(self, connectors, config: FundingOpenConfig):
        super().__init__(connectors)
        self.config = config
        self.operation_count = 0  # 操作次数计数（开仓或关仓）
        self.consecutive_count = 0
        self.set_leverage_flag = False
        self.is_operating = False  # 是否正在执行操作（开仓或关仓）
        self.stop_flag = False
        self.completed_orders = {}  # 保存已完成订单
        self.pending_operation = None    # 记录当前操作的订单ID及状态

    # 新增：事件驱动的订单完成处理
    def did_complete_buy_order(self, event):
        self.logger().info(f"买单完成，订单ID：{event.order_id}，成交数量：{event.base_asset_amount}，成交金额：{event.quote_asset_amount}")
        self.completed_orders[event.order_id] = event
        if self.pending_operation and self.pending_operation.get("spot") == event.order_id:
            self.pending_operation["spot_done"] = True
            self._try_finalize_operation()
        elif self.pending_operation and self.pending_operation.get("perp") == event.order_id:
            self.pending_operation["perp_done"] = True
            self._try_finalize_operation()

    def did_complete_sell_order(self, event):
        self.logger().info(f"卖单完成，订单ID：{event.order_id}，成交数量：{event.base_asset_amount}，成交金额：{event.quote_asset_amount}")
        self.completed_orders[event.order_id] = event
        if self.pending_operation and self.pending_operation.get("spot") == event.order_id:
            self.pending_operation["spot_done"] = True
            self._try_finalize_operation()
        elif self.pending_operation and self.pending_operation.get("perp") == event.order_id:
            self.pending_operation["perp_done"] = True
            self._try_finalize_operation()

    def did_fail_order(self, event):
        self.logger().error(f"订单失败，ID：{event.order_id}，原因：{getattr(event, 'error_message', '')}，终止策略，请手工检查仓位是否平衡。")
        self.stop_flag = True
        return

    def _try_finalize_operation(self):
        if self.pending_operation and self.pending_operation.get("spot_done") and self.pending_operation.get("perp_done"):
            # 两边都成交，做后续风控、统计、价差判断等
            spot_event = self.completed_orders.get(self.pending_operation["spot"])
            perp_event = self.completed_orders.get(self.pending_operation["perp"])
            if spot_event and perp_event:
                spot_avg_price = spot_event.quote_asset_amount / spot_event.base_asset_amount if spot_event.base_asset_amount else None
                perp_avg_price = perp_event.quote_asset_amount / perp_event.base_asset_amount if perp_event.base_asset_amount else None
                self.logger().info(f"现货成交均价: {spot_avg_price}, 合约成交均价: {perp_avg_price}")
                if spot_avg_price is None or perp_avg_price is None:
                    self.logger().error(f"无法获取成交均价，终止本次{self.config.operation_type}操作。")
                    self.stop_flag = True
                else:
                    avg_price_diff = abs(perp_avg_price - spot_avg_price) / spot_avg_price
                    self.logger().info(f"成交均价价差: {avg_price_diff:.6f}, 阈值: {self.config.avg_price_diff_threshold}")
                    if avg_price_diff > self.config.avg_price_diff_threshold:
                        self.logger().error(f"成交均价价差超出阈值({self.config.avg_price_diff_threshold})，终止本次{self.config.operation_type}操作。")
                        self.stop_flag = True
                    else:
                        operation_name = "开仓" if self.config.operation_type == "open" else "关仓"
                        self.logger().info(f"本次{operation_name}全部成交，累计{operation_name}次数: {self.operation_count+1}/{self.config.max_round}")
                        self.operation_count += 1
            self.is_operating = False
            self.pending_operation = None
            if self.operation_count >= self.config.max_round:
                operation_name = "开仓" if self.config.operation_type == "open" else "关仓"
                self.logger().info(f"已达到最大{operation_name}次数，停止{operation_name}。")
                self.stop_flag = True

    def on_tick(self):
        if self.stop_flag:
            return

        spot_connector = self.connectors[self.config.spot_exchange]
        perp_connector = self.connectors[self.config.perp_exchange]
        if getattr(spot_connector, "network_status", None) != NetworkStatus.CONNECTED:
            self.logger().warning("现货网络未连接，跳过本次tick。")
            return
        if getattr(perp_connector, "network_status", None) != NetworkStatus.CONNECTED:
            self.logger().warning("合约网络未连接，跳过本次tick。")
            return
        if not self.set_leverage():
            return False
        try:
            spot_price = self.connectors[self.config.spot_exchange].get_mid_price(self.config.spot_trading_pair)
            perp_price = self.connectors[self.config.perp_exchange].get_mid_price(self.config.perp_trading_pair)
            if spot_price is None or perp_price is None:
                self.logger().warning("无法获取价格，跳过本次tick。")
                return
            spread = (perp_price - spot_price) / spot_price
            self.logger().info(f"现货价格: {spot_price} 合约价格: {perp_price} 价差: {spread:.4f}")

            # 根据操作类型判断价差条件
            spread_condition_met = False
            if self.config.operation_type == "open":
                spread_condition_met = spread >= self.config.spread_threshold
            else:  # close
                spread_condition_met = spread <= self.config.spread_threshold

            if spread_condition_met:
                self.consecutive_count += 1
                operation_name = "开仓" if self.config.operation_type == "open" else "关仓"
                self.logger().info(f"价差连续满足{operation_name}条件: {self.consecutive_count}/{self.config.consecutive_threshold}")
            else:
                self.consecutive_count = 0

            if self.consecutive_count >= self.config.consecutive_threshold and self.operation_count < self.config.max_round:
                if self.is_operating:
                    operation_name = "开仓" if self.config.operation_type == "open" else "关仓"
                    self.logger().info(f"已有{operation_name}流程在进行，跳过本次。")
                    return

                # 关仓前检查余额
                if self.config.operation_type == "close":
                    if not self._check_balance_for_close():
                        self.logger().info("余额不足，无法继续关仓，停止策略。")
                        self.stop_flag = True
                        return

                self.is_operating = True
                operation_name = "开仓" if self.config.operation_type == "open" else "关仓"
                self.logger().info(f"满足{operation_name}条件，准备{operation_name}...")
                asyncio.create_task(self._operation_wrapper())
                self.consecutive_count = 0

            if self.operation_count >= self.config.max_round:
                operation_name = "开仓" if self.config.operation_type == "open" else "关仓"
                self.logger().info(f"已达到最大{operation_name}次数，停止{operation_name}。")
                self.stop_flag = True
        except Exception as e:
            self.logger().error(f"on_tick异常: {e}")

    def _check_balance_for_close(self):
        """检查关仓所需的余额是否足够"""
        try:
            spot_connector = self.connectors[self.config.spot_exchange]
            perp_connector = self.connectors[self.config.perp_exchange]

            # 获取现货余额
            spot_base_asset = self.config.spot_trading_pair.split('-')[0]
            spot_balance = spot_connector.get_available_balance(spot_base_asset)

            # 获取合约仓位
            perp_position = perp_connector.get_position(self.config.perp_trading_pair)
            perp_position_amount = abs(perp_position.amount) if perp_position else 0

            spot_amount = self.config.base_amount
            perp_amount = spot_amount / self.config.quantity_ratio

            self.logger().info(f"关仓检查 - 现货余额: {spot_balance}, 需要: {spot_amount}, 合约仓位: {perp_position_amount}, 需要: {perp_amount}")

            if spot_balance < spot_amount:
                self.logger().warning(f"现货余额不足，当前: {spot_balance}, 需要: {spot_amount}")
                return False

            if perp_position_amount < perp_amount:
                self.logger().warning(f"合约仓位不足，当前: {perp_position_amount}, 需要: {perp_amount}")
                return False

            return True
        except Exception as e:
            self.logger().error(f"检查余额异常: {e}")
            return False

    def set_leverage(self):
        if self.set_leverage_flag:
            return True
        else:
            set_result = self.connectors[self.config.perp_exchange].set_leverage(
                trading_pair=self.config.perp_trading_pair,
                leverage=self.config.leverage
            )
            if hasattr(set_result, "__await__"):  # 兼容异步
                success, msg = asyncio.get_event_loop().run_until_complete(set_result)
            else:
                success, msg = set_result if isinstance(set_result, tuple) else (True, "")
            if not success:
                self.logger().error(f"设置杠杆失败: {msg}")
                return False
            self.set_leverage_flag = True
            self.logger().info(f"杠杆设置成功: {self.config.leverage}x")

    async def execute_operation(self) -> bool:
        """执行操作（开仓或关仓）"""
        try:
            spot_amount = self.config.base_amount
            perp_amount = spot_amount / self.config.quantity_ratio

            if self.config.operation_type == "open":
                # 开仓：现货买入，合约做空
                spot_side = "buy"
                perp_side = "sell"
                operation_name = "开仓"
                debug_msg = f"现货买入{spot_amount}，合约做空{perp_amount}"
            else:  # close
                # 关仓：现货卖出，合约平仓（买入）
                spot_side = "sell"
                perp_side = "buy"
                operation_name = "关仓"
                debug_msg = f"现货卖出{spot_amount}，合约平仓{perp_amount}"

            if self.config.debug_mode:
                await asyncio.sleep(3)
                self.logger().info(f"[模拟下单] {debug_msg}")
                self.operation_count += 1
                self.is_operating = False
                return True

            # 并发下单
            spot_order_task = self.place_order(
                connector_name=self.config.spot_exchange,
                trading_pair=self.config.spot_trading_pair,
                amount=spot_amount,
                side=spot_side,
                order_type="market"
            )
            perp_order_task = self.place_order(
                connector_name=self.config.perp_exchange,
                trading_pair=self.config.perp_trading_pair,
                amount=perp_amount,
                side=perp_side,
                order_type="market",
                is_close=(self.config.operation_type == "close")
            )

            # 等待两个订单同时完成
            spot_order_id, perp_order_id = await asyncio.gather(spot_order_task, perp_order_task)

            if not spot_order_id:
                self.logger().error(f"现货下单失败，终止本次{operation_name}。")
                self.is_operating = False
                return False
            if not perp_order_id:
                self.logger().error(f"合约下单失败，终止本次{operation_name}。")
                self.is_operating = False
                return False
            self.pending_operation = {
                "spot": spot_order_id,
                "perp": perp_order_id,
                "spot_done": False,
                "perp_done": False,
            }
            self.logger().info(f"已下单，等待成交事件回调。现货订单ID: {spot_order_id}，合约订单ID: {perp_order_id}")
            return True
        except Exception as e:
            operation_name = "开仓" if self.config.operation_type == "open" else "关仓"
            self.logger().error(f"{operation_name}异常: {e}")
            self.is_operating = False
            return False

    async def _operation_wrapper(self):
        success = await self.execute_operation()
        if not success:
            self.stop_flag = True
        self.is_operating = False

    async def place_order(self, connector_name: str, trading_pair: str, amount: float, side: str, order_type: str, is_close: bool = False) -> Optional[str]:
        try:
            if self.config.debug_mode:
                await asyncio.sleep(3)
                action_desc = "平仓" if is_close else "开仓"
                print(f"[模拟下单] 交易所: {connector_name}, 交易对: {trading_pair}, 数量: {amount}, 方向: {side}, 类型: {order_type}, 动作: {action_desc}")
                return f"mock_order_{connector_name}_{trading_pair}_{side}_{amount}"
            else:
                connector = self.connectors[connector_name]
                order_type_enum = OrderType[order_type.upper()]
                amount_decimal = Decimal(str(amount))

                # 确定仓位动作
                position_action = None
                if connector_name == self.config.perp_exchange:
                    if is_close:
                        position_action = PositionAction.CLOSE
                    else:
                        position_action = PositionAction.OPEN

                if side == "buy":
                    order_id = connector.buy(
                        trading_pair=trading_pair,
                        amount=amount_decimal,
                        order_type=order_type_enum,
                        position_action=position_action
                    )
                else:
                    order_id = connector.sell(
                        trading_pair=trading_pair,
                        amount=amount_decimal,
                        order_type=order_type_enum,
                        position_action=position_action
                    )
                return order_id
        except Exception as e:
            self.logger().error(f"下单异常: {connector_name} {trading_pair} {side} {amount}: {e}")
            return None